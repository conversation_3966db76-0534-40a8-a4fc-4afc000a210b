<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-09T13:47:10.650040" rpa="false" schemaversion="5">
<suite id="s1" name="Get" source="C:\Users\<USER>\Desktop\Test_automation-HS_hcca-AUTOMATION\get.robot">
<test id="s1-t1" name="Houston Automation Test" line="61">
<kw name="Clean Environment" type="SETUP">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T13:47:12.147474" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-09T13:47:12.158478" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T13:47:12.413188" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T13:47:12.147474" elapsed="0.265714"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:47:14.414332" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:47:12.414132" elapsed="2.000200"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:47:14.415262" level="INFO">✅ Killed process: houston_server.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:47:14.414332" elapsed="0.000930"/>
</kw>
<arg>houston_server.exe</arg>
<status status="PASS" start="2025-06-09T13:47:12.145477" elapsed="2.269785"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T13:47:14.417261" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-09T13:47:14.428228" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T13:47:14.675385" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T13:47:14.416260" elapsed="0.259125"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:47:16.687463" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:47:14.676339" elapsed="2.011124"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:47:16.688453" level="INFO">✅ Killed process: houston_app.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:47:16.687463" elapsed="0.000990"/>
</kw>
<arg>houston_app.exe</arg>
<status status="PASS" start="2025-06-09T13:47:14.416260" elapsed="2.272193"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:47:16.689450" level="INFO">🧹 Environment cleaned</msg>
<arg>🧹 Environment cleaned</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:47:16.688453" elapsed="0.000997"/>
</kw>
<status status="PASS" start="2025-06-09T13:47:12.144478" elapsed="4.544972"/>
</kw>
<kw name="Start Houston Server">
<kw name="Start Process" owner="Process">
<msg time="2025-06-09T13:47:16.693451" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-09T13:47:16.704111" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-09T13:47:16.690457" elapsed="0.013654"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:47:26.715991" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:47:16.705098" elapsed="10.010893"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:47:26.718098" level="INFO">🚀 Houston Server started successfully</msg>
<arg>🚀 Houston Server started successfully</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:47:26.717102" elapsed="0.000996"/>
</kw>
<return>
<value>${hs_handle}</value>
<status status="PASS" start="2025-06-09T13:47:26.719096" elapsed="0.000000"/>
</return>
<msg time="2025-06-09T13:47:26.720094" level="INFO">${hs} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs}</var>
<status status="PASS" start="2025-06-09T13:47:16.690457" elapsed="10.029637"/>
</kw>
<kw name="Start HCCA">
<kw name="Start Process" owner="Process">
<msg time="2025-06-09T13:47:26.725086" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-09T13:47:26.748044" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-09T13:47:26.723084" elapsed="0.024960"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:47:31.761858" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:47:26.749041" elapsed="5.013752"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:47:31.763838" level="INFO">🚀 HCCA started successfully</msg>
<arg>🚀 HCCA started successfully</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:47:31.762793" elapsed="0.001045"/>
</kw>
<return>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-09T13:47:31.764836" elapsed="0.000000"/>
</return>
<msg time="2025-06-09T13:47:31.765839" level="INFO">${hcca} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca}</var>
<status status="PASS" start="2025-06-09T13:47:26.722097" elapsed="5.043742"/>
</kw>
<kw name="Click Search And Type">
<kw name="Open Eyes">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:47:31.770840" level="INFO">👁️ Opening eyes for visual recognition</msg>
<arg>👁️ Opening eyes for visual recognition</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:47:31.770840" elapsed="0.000996"/>
</kw>
<status status="PASS" start="2025-06-09T13:47:31.768839" elapsed="0.002997"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:47:51.778570" level="INFO">Slept 20 seconds.</msg>
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:47:31.772833" elapsed="20.006513"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Find Element" owner="Desktop">
<msg time="2025-06-09T13:47:51.784334" level="INFO">Resolving locator: C:\Users\<USER>\Desktop\Test_automation-HS_hcca-AUTOMATION C:\Users\<USER>\Desktop\Test_automation-HS_hcca-AUTOMATION\search_field.png</msg>
<msg time="2025-06-09T13:47:51.784334" level="FAIL">ValueError: Unknown locator type: c</msg>
<arg>${SEARCH_IMAGE}</arg>
<doc>Find an element defined by locator, and return its position.
Raises ``ElementNotFound`` if` no matches were found, or
``MultipleElementsFound`` if there were multiple matches.</doc>
<status status="FAIL" start="2025-06-09T13:47:51.781333" elapsed="0.010002">ValueError: Unknown locator type: c</status>
</kw>
<msg time="2025-06-09T13:47:51.792337" level="INFO">${status} = FAIL</msg>
<msg time="2025-06-09T13:47:51.792337" level="INFO">${element} = ValueError: Unknown locator type: c</msg>
<var>${status}</var>
<var>${element}</var>
<arg>Desktop.Find Element</arg>
<arg>${SEARCH_IMAGE}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-06-09T13:47:51.780342" elapsed="0.012992"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:48:06.800350" level="INFO">Slept 15 seconds.</msg>
<arg>15s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:47:51.793334" elapsed="15.009106"/>
</kw>
<kw name="Type Text" owner="Desktop">
<msg time="2025-06-09T13:48:06.821819" level="INFO">${variable} = None</msg>
<var>${variable}</var>
<arg>get_app_period</arg>
<doc>Type text one letter at a time.</doc>
<status status="PASS" start="2025-06-09T13:48:06.802440" elapsed="0.019379"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:48:06.822803" level="INFO">✅ Search term typed successfully</msg>
<arg>✅ Search term typed successfully</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:48:06.822803" elapsed="0.000000"/>
</kw>
<kw name="Close Eyes">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:48:06.825877" level="INFO">👁️ Closing eyes after visual operation</msg>
<arg>👁️ Closing eyes after visual operation</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:48:06.824801" elapsed="0.001076"/>
</kw>
<status status="PASS" start="2025-06-09T13:48:06.823818" elapsed="0.002059"/>
</kw>
<status status="PASS" start="2025-06-09T13:47:31.767793" elapsed="35.059086"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:48:09.831641" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:48:06.827826" elapsed="3.003815"/>
</kw>
<kw name="Clean Environment" type="TEARDOWN">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T13:48:09.838724" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-09T13:48:09.857657" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T13:48:10.094527" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T13:48:09.836725" elapsed="0.257802"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:48:12.106073" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:48:10.095526" elapsed="2.010547"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:48:12.107164" level="INFO">✅ Killed process: houston_server.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:48:12.107164" elapsed="0.000000"/>
</kw>
<arg>houston_server.exe</arg>
<status status="PASS" start="2025-06-09T13:48:09.835727" elapsed="2.272423"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T13:48:12.110156" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-09T13:48:12.123519" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T13:48:12.362108" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T13:48:12.109147" elapsed="0.253961"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T13:48:14.375628" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T13:48:12.363108" elapsed="2.013366"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:48:14.378386" level="INFO">✅ Killed process: houston_app.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:48:14.377390" elapsed="0.000996"/>
</kw>
<arg>houston_app.exe</arg>
<status status="PASS" start="2025-06-09T13:48:12.108150" elapsed="2.270236"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T13:48:14.380455" level="INFO">🧹 Environment cleaned</msg>
<arg>🧹 Environment cleaned</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T13:48:14.379455" elapsed="0.001000"/>
</kw>
<status status="PASS" start="2025-06-09T13:48:09.834671" elapsed="4.545784"/>
</kw>
<status status="PASS" start="2025-06-09T13:47:12.142477" elapsed="62.238995"/>
</test>
<doc>Test to launch Houston Server, HCCA, and type in search field</doc>
<status status="PASS" start="2025-06-09T13:47:10.655039" elapsed="63.731676"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Get" id="s1" pass="1" fail="0" skip="0">Get</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
