*** Settings ***
Documentation     Test to launch Houston Server, HCCA, and type in search field
Library           Process
Library           OperatingSystem
Library           String
Library           RPA.Desktop    WITH NAME    Desktop

*** Variables ***
${HS_DIR}                 C:\\Users\\<USER>\\Desktop\\SX-Houston-server_v142\\SX-Houston-server_v142
${HCCA_DIR}               C:\\Users\\<USER>\\Desktop\\SX-Houston-app_v212
${HS_PATH}                ${HS_DIR}\\houston_server.exe
${HCCA_PATH}              ${HCCA_DIR}\\houston_app.exe
${SEARCH_IMAGE}           ${CURDIR} C:\\Users\\<USER>\\Desktop\\Test_automation-HS_hcca-AUTOMATION\\search_field.png

*** Keywords ***
Kill Process If Running
    [Arguments]    ${process_name}
    Run Process    taskkill    /F    /IM    ${process_name}    /T    shell=True
    Sleep    2s
    Log    ✅ Killed process: ${process_name}

Clean Environment
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    Log    🧹 Environment cleaned

Start Houston Server
    ${hs_handle}=    Start Process    ${HS_PATH}    shell=True    cwd=${HS_DIR}
    Sleep    10s
    Log    🚀 Houston Server started successfully
    RETURN    ${hs_handle}

Start HCCA
    ${hcca_handle}=    Start Process    ${HCCA_PATH}    shell=True    cwd=${HCCA_DIR}
    Sleep    5s
    Log    🚀 HCCA started successfully
    RETURN    ${hcca_handle}

Open Eyes
    # Implementation for Open Eyes
    Log    👁️ Opening eyes for visual recognition


Close Eyes
    # Implementation for Close Eyes
    Log    👁️ Closing eyes after visual operation


Click Search And Type
    Open Eyes
    Sleep    20s
    # Try to find the search field image
    ${status}    ${element}=    Run Keyword And Ignore Error    Desktop.Find Element    ${SEARCH_IMAGE}
    
    Sleep    15s
    ${variable}=    Desktop.Type Text    get_app_period
    Log    ✅ Search term typed successfully
    Close Eyes

*** Test Cases ***
Houston Automation Test
    [Setup]     Clean Environment
    ${hs}=      Start Houston Server
    ${hcca}=    Start HCCA
    Click Search And Type
    Sleep       3s
    [Teardown]  Clean Environment
